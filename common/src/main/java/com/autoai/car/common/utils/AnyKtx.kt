package com.autoai.car.common.utils

import android.os.Looper
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlin.coroutines.CoroutineContext


/**
 * 检查内存压力状态的简单实现
 * 避免对app模块的依赖
 */
private fun isMemoryPressureHigh(): <PERSON><PERSON><PERSON> {
    return try {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val memoryUsageRatio = usedMemory.toFloat() / maxMemory.toFloat()

        // 如果内存使用率超过75%，认为处于内存压力状态
        memoryUsageRatio >= 0.75f
    } catch (e: Exception) {
        // 如果检查内存状态时出现异常，保守地认为处于内存压力状态
        true
    }
}

/**
 * 全局协程异常处理器
 * 统一处理所有协程中的未捕获异常，防止crash传播到系统级处理器
 *
 * 增强版本：
 * 1. 完全消化异常，防止传播到FireEye
 * 2. 异步处理异常信息，避免阻塞
 * 3. 增加主线程检测和保护
 */
private val GlobalCoroutineExceptionHandler = CoroutineExceptionHandler { context, throwable ->
    try {
        // 检查是否在主线程
        val isMainThread = Looper.myLooper() == Looper.getMainLooper()

        // 立即记录关键信息，使用Android原生Log确保记录成功
        LogUtils.e(
            "GlobalCoroutineExceptionHandler",
            "Coroutine exception caught (mainThread=$isMainThread): ${throwable.javaClass.simpleName}"
        )

        // 异步处理详细信息，避免阻塞当前线程
        CoroutineScope(Dispatchers.IO + SupervisorJob()).launch {
            try {
                handleCoroutineExceptionDetails(context, throwable, isMainThread)
            } catch (e: Exception) {
                // 异步处理失败时的备用记录
                LogUtils.e("GlobalCoroutineExceptionHandler", "Failed to handle exception details: $e")
            }
        }

        // 重要：异常已被完全处理，不再向上传播
        // 这样可以防止异常传播到Thread.UncaughtExceptionHandler（包括FireEye）

    } catch (e: Exception) {
        // 异常处理器本身出现异常时的最后防线
        try {
            LogUtils.e("GlobalCoroutineExceptionHandler", "Critical error in exception handler: $e")
            LogUtils.e("GlobalCoroutineExceptionHandler", "Original exception: $throwable")
        } catch (logError: Exception) {
            // 连日志都无法记录时，静默处理，避免无限递归
        }
    }
}

/**
 * 处理协程异常的详细信息
 * 在后台线程异步执行，避免阻塞
 */
private fun handleCoroutineExceptionDetails(
    context: CoroutineContext,
    throwable: Throwable,
    isMainThread: Boolean
) {
    try {
        // 检查内存压力，决定处理策略
        if (isMemoryPressureHigh()) {
            // 内存压力高时，只记录简单信息
            LogUtils.e(
                "GlobalCoroutineExceptionHandler",
                "Coroutine exception under memory pressure: ${throwable.javaClass.simpleName} (mainThread=$isMainThread)"
            )
        } else {
            // 正常情况下记录详细异常信息
            val contextInfo = context.toString()
            LogUtils.e(
                "GlobalCoroutineExceptionHandler",
                "Uncaught coroutine exception (mainThread=$isMainThread)\nContext: $contextInfo", throwable
            )

            // 记录内存状态，帮助分析问题
            logMemoryStatusForException()
        }
    } catch (e: Exception) {
        LogUtils.e("GlobalCoroutineExceptionHandler", "Error handling exception details: $e")
    }
}

/**
 * 记录异常发生时的内存状态
 */
private fun logMemoryStatusForException() {
    try {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val memoryUsageRatio = usedMemory.toFloat() / maxMemory.toFloat()

        LogUtils.d(
            "GlobalCoroutineExceptionHandler",
            "Memory at exception: ${(memoryUsageRatio * 100).toInt()}% (${usedMemory / 1024 / 1024}MB / ${maxMemory / 1024 / 1024}MB)"
        )
    } catch (e: Exception) {
        // 内存状态记录失败时静默处理
    }
}

/**
 * 主线程的协程 - 带异常处理器
 *
 * @param callback
 */
fun mainLaunch(callback: suspend () -> Unit) {
    CoroutineScope(Dispatchers.Main + GlobalCoroutineExceptionHandler).launch {
        try {
            callback.invoke()
        } catch (e: Exception) {
            // 双重保护：try-catch + 异常处理器
            LogUtils.e("mainLaunch", "Exception in main coroutine", e)
        }
    }
}

/**
 * IO线程的协程 - 使用守护线程避免阻塞应用关闭，带异常处理器
 *
 * @param callback
 */
fun ioLaunch(callback: suspend () -> Unit) {
    CoroutineScope(DaemonThreadDispatcher.IO + GlobalCoroutineExceptionHandler).launch {
        try {
            callback.invoke()
        } catch (e: Exception) {
            // 双重保护：try-catch + 异常处理器
            LogUtils.e("ioLaunch", "Exception in IO coroutine", e)
        }
    }
}


// 使用守护线程的单线程调度器，避免阻塞应用关闭
private val singleThreadDispatcher = DaemonThreadDispatcher.Single

/**
 * IO单线程，排队执行 - 使用守护线程避免阻塞应用关闭，带异常处理器
 */
fun ioSingleLaunch(callback: suspend () -> Unit) {
    CoroutineScope(singleThreadDispatcher + GlobalCoroutineExceptionHandler).launch {
        try {
            callback.invoke()
        } catch (e: Exception) {
            // 双重保护：try-catch + 异常处理器
            LogUtils.e("ioSingleLaunch", "Exception in IO single coroutine", e)
        }
    }
}

/**
 * default线程的协程 - 使用守护线程避免阻塞应用关闭，带异常处理器
 *
 * @param callback
 */
fun defaultLaunch(callback: suspend () -> Unit) {
    CoroutineScope(DaemonThreadDispatcher.Default + GlobalCoroutineExceptionHandler).launch {
        try {
            callback.invoke()
        } catch (e: Exception) {
            // 双重保护：try-catch + 异常处理器
            LogUtils.e("defaultLaunch", "Exception in default coroutine", e)
        }
    }
}