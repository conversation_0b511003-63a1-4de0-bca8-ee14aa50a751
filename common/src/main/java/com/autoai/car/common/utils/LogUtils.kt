package com.autoai.car.common.utils

import android.util.Log

/**
 * @author: 董俊帅
 * @time: 2025/8/26
 * @desc: 日志工具类
 */
object LogUtils {

    private const val PACKAGE_NAME = "com.autoai.aiosservice-"

    fun isLoggable(): Boolean {
        return true
    }

    fun d(tag: String?, msg: String) {
        if (!isLoggable()) {
            return
        }
        Log.d(PACKAGE_NAME + tag, msg)
    }

    fun e(tag: String?, msg: String) {
        if (!isLoggable()) {
            return
        }
        Log.e(PACKAGE_NAME + tag, msg)
    }

    fun i(tag: String?, msg: String) {
        if (!isLoggable()) {
            return
        }
        Log.i(PACKAGE_NAME + tag, msg)
    }

    fun w(tag: String?, msg: String) {
        if (!isLoggable()) {
            return
        }
        Log.w(PACKAGE_NAME + tag, msg)
    }

    fun v(tag: String?, msg: String) {
        if (!isLoggable()) {
            return
        }
        Log.v(PACKAGE_NAME + tag, msg)
    }

    fun d(tag: String?, msg: String, tr: Throwable) {
        if (!isLoggable()) {
            return
        }
        Log.d(PACKAGE_NAME + tag, msg, tr)
    }

    fun e(tag: String?, msg: String, tr: Throwable) {
        if (!isLoggable()) {
            return
        }
        Log.e(PACKAGE_NAME + tag, msg, tr)
    }

    fun i(tag: String?, msg: String, tr: Throwable) {
        if (!isLoggable()) {
            return
        }
        Log.i(PACKAGE_NAME + tag, msg, tr)
    }

    fun w(tag: String?, msg: String, tr: Throwable) {
        if (!isLoggable()) {
            return
        }
        Log.w(PACKAGE_NAME + tag, msg, tr)
    }

    fun v(tag: String?, msg: String, tr: Throwable) {
        if (!isLoggable()) {
            return
        }
        Log.v(PACKAGE_NAME + tag, msg, tr)
    }

    fun i(tr: Throwable) {
        if (!isLoggable()) {
            return
        }
        Log.i(PACKAGE_NAME, tr.message ?: "", tr)
    }

    fun d(tr: Throwable) {
        if (!isLoggable()) {
            return
        }
        Log.d(PACKAGE_NAME, tr.message ?: "", tr)
    }

    fun w(tr: Throwable) {
        if (!isLoggable()) {
            return
        }
        Log.w(PACKAGE_NAME, tr.message ?: "", tr)
    }

    fun e(tr: Throwable) {
        if (!isLoggable()) {
            return
        }
        Log.e(PACKAGE_NAME, tr.message ?: "", tr)
    }

    fun v(tr: Throwable) {
        if (!isLoggable()) {
            return
        }
        Log.v(PACKAGE_NAME, tr.message ?: "", tr)
    }

    fun v(str: String) {
        if (!isLoggable()) {
            return
        }
        Log.v(PACKAGE_NAME, str)
    }

    fun d(str: String) {
        if (!isLoggable()) {
            return
        }
        Log.d(PACKAGE_NAME, str)
    }

    fun i(str: String) {
        if (!isLoggable()) {
            return
        }
        Log.i(PACKAGE_NAME, str)
    }

    fun w(str: String) {
        if (!isLoggable()) {
            return
        }
        Log.w(PACKAGE_NAME, str)
    }

    fun e(str: String) {
        if (!isLoggable()) {
            return
        }
        Log.e(PACKAGE_NAME, str)
    }

}