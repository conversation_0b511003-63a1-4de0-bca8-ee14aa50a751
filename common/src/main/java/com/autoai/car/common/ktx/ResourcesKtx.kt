package com.autoai.car.common.ktx

import android.content.res.Resources
import androidx.annotation.ColorRes
import androidx.annotation.DimenRes

/**
 * @author: 董俊帅
 * @time: 2025/8/26
 * @desc: 资源相关扩展方法
 */
// 安全获取颜色（返回 null 或默认值）
fun Resources.getColorOrNull(@ColorRes id: Int): Int? {
    return try {
        getColor(id)
    } catch (e: Resources.NotFoundException) {
        null
    }
}

// 安全获取尺寸（返回 null）
fun Resources.getDimensionOrNull(@DimenRes id: Int): Float? {
    return try {
        getDimension(id)
    } catch (e: Resources.NotFoundException) {
        null
    }
}

// 安全获取像素尺寸（返回 null）
fun Resources.getDimensionPixelSizeOrNull(@DimenRes id: Int): Int? {
    return try {
        getDimensionPixelSize(id)
    } catch (e: Resources.NotFoundException) {
        null
    }
}