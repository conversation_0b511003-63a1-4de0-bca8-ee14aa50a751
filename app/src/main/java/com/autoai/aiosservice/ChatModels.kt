package com.autoai.aiosservice

import com.google.gson.annotations.SerializedName

/**
 * @author: 董俊帅
 * @time: 2025/8/29
 * @desc: 聊天API相关数据模型
 */

/**
 * 聊天消息
 */
data class ChatMessage(
    @SerializedName("role")
    val role: String,
    @SerializedName("content")
    val content: String
)

/**
 * 聊天请求
 */
data class ChatRequest(
    @SerializedName("model")
    val model: String,
    @SerializedName("stream")
    val stream: Boolean,
    @SerializedName("messages")
    val messages: List<ChatMessage>
)

/**
 * 聊天响应
 */
data class ChatResponse(
    @SerializedName("model")
    val model: String? = null,
    @SerializedName("created_at")
    val createdAt: String? = null,
    @SerializedName("message")
    val message: ChatMessage? = null,
    @SerializedName("done")
    val done: Boolean? = null,
    @SerializedName("total_duration")
    val totalDuration: Long? = null,
    @SerializedName("load_duration")
    val loadDuration: Long? = null,
    @SerializedName("prompt_eval_count")
    val promptEvalCount: Int? = null,
    @SerializedName("prompt_eval_duration")
    val promptEvalDuration: Long? = null,
    @SerializedName("eval_count")
    val evalCount: Int? = null,
    @SerializedName("eval_duration")
    val evalDuration: Long? = null
)
