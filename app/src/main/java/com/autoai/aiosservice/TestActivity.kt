package com.autoai.aiosservice

import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.autoai.aiosservice.ChatMessage
import com.autoai.aiosservice.core.prompt.PromptManager
import com.autoai.aiosservice.databinding.ActivityTestBinding
import com.autoai.aiosservice.tools.ResponseData
import com.autoai.aiosservice.tools.VoiceEngineTools
import com.autoai.car.common.utils.LogUtils
import com.autoai.car.common.utils.ioLaunch
import com.autoai.car.common.utils.mainLaunch
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import java.util.concurrent.TimeUnit

/**
 * @author: 董俊帅
 * @time: 2025/8/26
 * @desc: 测试页面
 */
class TestActivity: AppCompatActivity() {

    private lateinit var binding: ActivityTestBinding
    
    // 懒加载创建Retrofit实例，避免重复创建
    private val chatApiService: ChatApiService by lazy {
        createChatApiService()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTestBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initView()
    }

    private fun initView() {
        binding.btnSendPrompt.setOnClickListener {
            // 发送Prompt
            LogUtils.d(TAG, "initView: 发送Prompt")
            sendChatRequest()
        }

        binding.btnGetTools.setOnClickListener {
            // 获取Tools
            LogUtils.d(TAG, "initView: 获取Tools")
            getTools()
        }
    }

    private fun getTools() {
        val params = mapOf(
            "domain" to "VehicleControl",
            "user_input" to "车内温度太高了"
        )
        LogUtils.d(TAG, "getTools: request params - $params")
        VoiceEngineTools.requestTools(params, "1234567890", object : VoiceEngineTools.ToolsCallback {
            override fun onSuccess(data: ResponseData) {
                // 处理成功响应
                LogUtils.d(TAG, "getTools: onSuccess - error_code: ${data.error_code}, error_message: ${data.error_message}, content: ${data.content}")
                if (data.error_code != 0) {
                    LogUtils.e(TAG, "getTools: server error - code: ${data.error_code}, message: ${data.error_message}")
                    runOnUiThread {
                        Toast.makeText(this@TestActivity, "获取Tools失败: 错误码${data.error_code} - ${data.error_message}", Toast.LENGTH_LONG).show()
                    }
                } else {
                    LogUtils.d(TAG, "getTools: success - ${data.content}")
                    runOnUiThread {
                        Toast.makeText(this@TestActivity, "获取Tools成功: ${data.content}", Toast.LENGTH_LONG).show()
                    }
                }
            }

            override fun onFailure(e: Exception) {
                // 处理错误
                LogUtils.e(TAG, "getTools: onFailure - ${e.message}", e)
                runOnUiThread {
                    Toast.makeText(this@TestActivity, "获取Tools失败: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        })

    }
    
    /**
     * 创建聊天API服务
     */
    private fun createChatApiService(): ChatApiService {
        // 创建日志拦截器
        val loggingInterceptor = HttpLoggingInterceptor { message ->
            LogUtils.d(TAG, "HTTP: $message")
        }.apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
        
        // 创建OkHttp客户端
        val okHttpClient = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .build()
        
        // 创建Retrofit实例
        val retrofit = Retrofit.Builder()
            .baseUrl("http://10.79.29.200:11434/")
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
        
        return retrofit.create(ChatApiService::class.java)
    }
    
    /**
     * 发送聊天请求
     */
    private fun sendChatRequest() {
        ioLaunch {
            try {
                LogUtils.d(TAG, "sendChatRequest: 开始发送网络请求")
                
                // 构建请求数据
                val systemMessage = ChatMessage(
                    role = "system",
                    content = PromptManager.systemPrompt
                )
                val systemMessage2 = ChatMessage(
                    role = "system",
                    content = PromptManager.systemPrompt2
                )
                // 构建请求数据
                val chatMessage = ChatMessage(
                    role = "user",
                    content = "我好热呀"
                )
                
                val chatRequest = ChatRequest(
                    model = "qwen2.5vl:3b",
                    stream = false,
                    messages = listOf(systemMessage, systemMessage2,chatMessage)
                )
                
                LogUtils.d(TAG, "sendChatRequest: 请求数据构建完成 - model: ${chatRequest.model}, message: ${chatMessage.content}")
                
                // 发送网络请求
                val response = chatApiService.chat(chatRequest)
                
                // 切换到主线程处理结果
                mainLaunch {
                    handleChatResponse(response)
                }
                
            } catch (e: Exception) {
                LogUtils.e(TAG, "sendChatRequest: 网络请求失败", e)
                mainLaunch {
                    LogUtils.e(TAG, "Chat request failed: ${e.message}")
                }
            }
        }
    }
    
    /**
     * 处理聊天响应
     */
    private fun handleChatResponse(response: ChatResponse) {
        LogUtils.d(TAG, "handleChatResponse: 收到响应")
        LogUtils.d(TAG, "Response model: ${response.model}")
        LogUtils.d(TAG, "Response message role: ${response.message?.role}")
        LogUtils.d(TAG, "Response message content: ${response.message?.content}")
        LogUtils.d(TAG, "Response done: ${response.done}")
        LogUtils.d(TAG, "Response created_at: ${response.createdAt}")
        
        // 打印性能相关信息
        response.totalDuration?.let { 
            LogUtils.d(TAG, "Total duration: ${it / 1_000_000}ms") 
        }
        response.loadDuration?.let { 
            LogUtils.d(TAG, "Load duration: ${it / 1_000_000}ms") 
        }
        response.evalDuration?.let { 
            LogUtils.d(TAG, "Eval duration: ${it / 1_000_000}ms") 
        }
        response.promptEvalDuration?.let { 
            LogUtils.d(TAG, "Prompt eval duration: ${it / 1_000_000}ms") 
        }
        
        LogUtils.i(TAG, "Chat response received successfully: ${response.message?.content ?: "No content"}")
    }

    companion object {
        private const val TAG = "TestActivity"
    }
}
