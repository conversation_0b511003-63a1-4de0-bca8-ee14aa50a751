package com.autoai.aiosservice.tools

import com.autoai.car.common.utils.LogUtils
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import java.nio.charset.StandardCharsets
import java.security.InvalidKeyException
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.util.*
import java.util.concurrent.TimeUnit
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

/**
 * @author: <PERSON><PERSON>
 * @date: 2025/09/01
 * @desc: 语音引擎工具类，用于与语音服务进行交互
 */
object VoiceEngineTools {
    private const val AK = "navinfo81579a31bd5419362b350e9b4"
    private const val SK = "88fc1876ca3e5789ccd7fc5889d20cdc"
    private const val CHANNEL = "navinfo"
    private const val APP_ID = 99410 // 之前颁发的aid
    private const val VEHICLE_ID = "121212"
    private const val REJECTION_URL = "https://api-vehicle-cloud.volcengine.com/dpfm/v1/chat/intent"
    private const val CHAT_URL = "https://api-vehicle-cloud.volcengine.com/dpfm/v1/plugin/do/prepare"
    private const val TOOL_URL = "https://api-vehicle-cloud.volcengine.com/dpfm/v1/plugin/do/stream"

    // 创建 OkHttpClient 实例
    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()

    /**
     * 请求工具接口
     * @param params 请求参数
     * @param deviceId 设备ID
     * @param callback 回调接口
     */
    fun requestTools(params: Map<String, Any>, deviceId: String, callback: ToolsCallback) {
        try {
            val timestamp = System.currentTimeMillis() / 1000
            val randomInteger = Random().nextInt(65536)
            val queryStr = String.format(
                "_timestamp=%d&_nonce=%d&channel=%s&app_id=%d&vehicle_id=%s",
                timestamp, randomInteger, CHANNEL, APP_ID, deviceId
            )

            val body = Gson().toJson(params)
            LogUtils.d("VoiceEngineTools", "Request body: $body")

            // 生成签名
            val signature = "$AK:${genSign("POST", queryStr, SK, body)}"
            LogUtils.d("VoiceEngineTools", "Signature: $signature")

            // 构建请求体
            val requestBody = body.toRequestBody("application/json; charset=utf-8".toMediaType())

            // 构建请求
            val url = "$TOOL_URL?$queryStr"
            LogUtils.d("VoiceEngineTools", "Request URL: $url")
            
            val request = Request.Builder()
                .url(url)
                .post(requestBody)
                .addHeader("Content-Type", "application/json")
                .addHeader("X-Signature", signature)
                .addHeader("X-Use-PPE", "1")
                .addHeader("X-Tt-Env", "ppe_vehicle_model_test")
                .build()

            // 异步执行请求
            client.newCall(request).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    LogUtils.e("VoiceEngineTools", "Network request failed", e)
                    callback.onFailure(e)
                }

                override fun onResponse(call: Call, response: Response) {
                    try {
                        val responseBody = response.body
                        LogUtils.d("VoiceEngineTools", "Response code: ${response.code}")
                        if (!response.isSuccessful) {
                            LogUtils.e("VoiceEngineTools", "Unexpected response code: ${response.code}")
                            callback.onFailure(IOException("Unexpected code $response"))
                            return
                        }

                        if (responseBody == null) {
                            LogUtils.e("VoiceEngineTools", "Empty response body")
                            callback.onFailure(IOException("Empty response body"))
                            return
                        }

                        // 读取响应数据
                        val responseData = responseBody.string()
                        LogUtils.d("VoiceEngineTools", "Response data: $responseData")

                        // 尝试将响应数据解析为 ResponseData 对象
                        try {
                            // 首先检查响应数据是否为有效的JSON格式
                            if (responseData.startsWith("{") && responseData.endsWith("}")) {
                                val data = Gson().fromJson(responseData, ResponseData::class.java)
                                // 如果服务器返回错误码，将其作为错误处理
                                if (data.error_code != 0) {
                                    LogUtils.e("VoiceEngineTools", "Server error - code: ${data.error_code}, message: ${data.error_message}")
                                    callback.onSuccess(data) // 仍然调用onSuccess，因为这是服务器返回的有效响应
                                } else {
                                    callback.onSuccess(data)
                                }
                            } else {
                                // 如果不是JSON对象格式，创建一个包含原始响应数据的ResponseData对象
                                val data = ResponseData().apply {
                                    content = responseData
                                    error_message = "Response is not JSON format"
                                    error_code = -1
                                }
                                callback.onSuccess(data)
                            }
                        } catch (e: JsonSyntaxException) {
                            LogUtils.e("VoiceEngineTools", "Failed to parse response data: ${e.message}")
                            // 即使JSON解析失败，也将原始响应数据包装在ResponseData中返回
                            val data = ResponseData().apply {
                                content = responseData
                                error_message = "Failed to parse response data: ${e.message}"
                                error_code = -1
                            }
                            callback.onSuccess(data)
                        }
                    } catch (e: Exception) {
                        LogUtils.e("VoiceEngineTools", "Error processing response", e)
                        callback.onFailure(e)
                    }
                }
            })
        } catch (e: Exception) {
            LogUtils.e("VoiceEngineTools", "Error building request", e)
            callback.onFailure(e)
        }
    }

    /**
     * 生成签名
     * @param method HTTP方法
     * @param querystr 查询字符串
     * @param sk 密钥
     * @param body 请求体
     * @return 签名字符串
     */
    fun genSign(method: String, querystr: String, sk: String, body: String?): String {
        return try {
            // Step 1: URL encode the querystr with safe characters :/&=
//            val encodedQueryStr = customUrlEncode(querystr, ":/&=")

            // Step 2: Split by '&', sort, and rejoin
            val params = querystr.split("&").toTypedArray()
            Arrays.sort(params)
            val sortedQueryStr = params.joinToString("&")

            // Step 3: Build the string to sign
            val strToSign = StringBuilder()
            strToSign.append(method).append("\n")
                .append(sortedQueryStr).append("\n")

            // Step 4: Add MD5 of body if present
            if (body != null && body.isNotEmpty()) {
                val md = MessageDigest.getInstance("MD5")
                val digest = md.digest(body.toByteArray(StandardCharsets.UTF_8))
                val hex = bytesToHex(digest)
                strToSign.append(hex).append("\n")
            }

            // Step 5: Compute HMAC-SHA256
            val hmacSha256 = Mac.getInstance("HmacSHA256")
            val secretKey = SecretKeySpec(sk.toByteArray(StandardCharsets.UTF_8), "HmacSHA256")
            hmacSha256.init(secretKey)
            val hmacBytes = hmacSha256.doFinal(strToSign.toString().toByteArray(StandardCharsets.UTF_8))

            // Step 6: Base64 encode
            Base64.getEncoder().encodeToString(hmacBytes)
        } catch (e: NoSuchAlgorithmException) {
            throw RuntimeException("Error generating signature", e)
        } catch (e: InvalidKeyException) {
            throw RuntimeException("Error generating signature", e)
        }
    }

    private fun customUrlEncode(input: String, safeChars: String): String {
        val encoded = StringBuilder()
        for (c in input.toCharArray()) {
            if (isUnreserved(c) || safeChars.indexOf(c) >= 0) {
                encoded.append(c)
            } else {
                val bytes = c.toString().toByteArray(StandardCharsets.UTF_8)
                for (b in bytes) {
                    encoded.append('%')
                    encoded.append(String.format("%02X", b.toInt() and 0xFF))
                }
            }
        }
        return encoded.toString()
    }

    private fun isUnreserved(c: Char): Boolean {
        return Character.isLetterOrDigit(c)
    }

    private fun bytesToHex(bytes: ByteArray): String {
        val hexString = StringBuilder()
        for (b in bytes) {
            val hex = String.format("%02x", b)
            hexString.append(hex)
        }
        return hexString.toString()
    }

    /**
     * 工具回调接口
     */
    interface ToolsCallback {
        fun onSuccess(data: ResponseData)
        fun onFailure(e: Exception)
    }
}