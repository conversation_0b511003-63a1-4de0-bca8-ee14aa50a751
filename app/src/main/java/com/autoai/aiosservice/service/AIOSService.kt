package com.autoai.aiosservice.service

import android.app.Service
import android.content.Intent
import android.os.IBinder
import com.autoai.car.common.utils.LogUtils

/**
 * @author: 董俊帅
 * @time: 2025/8/27
 * @desc: 基础服务类，提供服务的基本实现
 */
abstract class AIOSService : Service() {

    override fun onCreate() {
        super.onCreate()
        LogUtils.d(TAG, "onCreate")
    }

    override fun onBind(intent: Intent?): IBinder? {
        LogUtils.d(TAG, "onBind")
        // 返回 null 表示不支持绑定
        return null
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        LogUtils.d(TAG, "onStartCommand")
        // 默认返回 START_NOT_STICKY，子类可以重写以改变行为
        return START_NOT_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        LogUtils.d(TAG, "onDestroy")
    }

    companion object {
        private const val TAG = "AIOSService"
    }
}